// Thunderbird Email Processing Utility
// Main processor for converting Thunderbird files to enhanced mbox format with threading

use std::fs;
use std::path::{Path, PathBuf};
use std::io;
use chrono::Utc;

use crate::{parse_mbox, ParsedEmail};
use crate::email_threading::{ThreadedEmail, EmailThreader, DuplicateDetector};
use crate::enhanced_mbox::EnhancedMboxWriter;
use mail_parser::{MessageParser, HeaderValue};

/// Processing mode for memory vs threading optimization
#[derive(Debug, Clone, PartialEq)]
pub enum ProcessingMode {
    /// Single-pass streaming (memory efficient, may miss some cross-file threading)
    SinglePass,
    /// Two-pass streaming (memory efficient + perfect threading)
    TwoPass,
    /// Year-based batch processing (controllable batches + perfect threading)
    YearBased { batch_size_years: u32 },
}

/// Main Thunderbird processor
pub struct ThunderbirdProcessor {
    threader: EmailThreader,
    duplicate_detector: DuplicateDetector,
    processed_count: u32,
    duplicate_count: u32,
    error_count: u32,
    processing_mode: ProcessingMode,
}

impl ThunderbirdProcessor {
    pub fn new() -> Self {
        Self::with_mode(ProcessingMode::SinglePass)
    }

    pub fn with_mode(mode: ProcessingMode) -> Self {
        Self {
            threader: EmailThreader::new(),
            duplicate_detector: DuplicateDetector::new(0.85), // 85% similarity threshold
            processed_count: 0,
            duplicate_count: 0,
            error_count: 0,
            processing_mode: mode,
        }
    }

    /// Process a Thunderbird directory structure using the configured processing mode
    pub fn process_thunderbird_directory(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        match &self.processing_mode {
            ProcessingMode::SinglePass => {
                self.process_single_pass(thunderbird_dir, output_mbox_path)
            }
            ProcessingMode::TwoPass => {
                self.process_two_pass(thunderbird_dir, output_mbox_path)
            }
            ProcessingMode::YearBased { batch_size_years } => {
                self.process_year_based(thunderbird_dir, output_mbox_path, *batch_size_years)
            }
        }
    }

    /// Single-pass streaming processing (original implementation)
    fn process_single_pass(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        println!("Processing Thunderbird directory (single-pass): {}", thunderbird_dir);

        let mut writer = EnhancedMboxWriter::new(output_mbox_path)?;
        let mut total_emails_processed = 0u32;

        // Find all mbox files in the directory structure
        let mbox_files = self.find_thunderbird_files(thunderbird_dir)?;
        println!("Found {} Thunderbird files to process", mbox_files.len());

        // Process each file individually to avoid memory accumulation
        for (file_path, folder_type) in mbox_files {
            println!("Processing file: {} (type: {:?})", file_path.display(), folder_type);

            match self.process_single_file_streaming(&file_path, folder_type, &mut writer) {
                Ok(file_email_count) => {
                    total_emails_processed += file_email_count;
                    println!("  Processed {} emails from {}", file_email_count, file_path.display());
                }
                Err(e) => {
                    eprintln!("Error processing file {}: {}", file_path.display(), e);
                    self.error_count += 1;
                }
            }
        }

        writer.close()?;

        println!("Total emails processed: {}", total_emails_processed);
        println!("Duplicates found: {}", self.duplicate_count);
        println!("Threads created: {}", self.threader.threads.len());
        println!("Cases created: {}", self.threader.cases.len());

        Ok(ProcessingReport {
            total_emails: total_emails_processed + self.duplicate_count,
            processed_emails: self.processed_count,
            duplicate_emails: self.duplicate_count,
            error_count: self.error_count,
            threads_created: self.threader.threads.len() as u32,
            cases_created: self.threader.cases.len() as u32,
            output_file: output_mbox_path.to_string(),
        })
    }

    /// Two-pass processing: first pass for initial processing, second pass for threading refinement
    fn process_two_pass(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        println!("Processing Thunderbird directory (two-pass): {}", thunderbird_dir);

        // First pass: Single-pass processing to create initial enhanced mbox
        let temp_output = format!("{}.temp", output_mbox_path);
        let first_pass_report = self.process_single_pass(thunderbird_dir, &temp_output)?;

        println!("First pass complete. Starting threading refinement pass...");

        // Second pass: Read the enhanced mbox and refine threading
        let refined_report = self.refine_threading(&temp_output, output_mbox_path)?;

        // Clean up temporary file
        if let Err(e) = std::fs::remove_file(&temp_output) {
            eprintln!("Warning: Failed to remove temporary file {}: {}", temp_output, e);
        }

        println!("Two-pass processing complete!");
        println!("Threading refinements made: {}", refined_report.threading_improvements);

        Ok(ProcessingReport {
            total_emails: first_pass_report.total_emails,
            processed_emails: first_pass_report.processed_emails,
            duplicate_emails: first_pass_report.duplicate_emails,
            error_count: first_pass_report.error_count + refined_report.error_count,
            threads_created: refined_report.threads_created,
            cases_created: refined_report.cases_created,
            output_file: output_mbox_path.to_string(),
        })
    }

    /// Year-based batch processing for very large datasets
    fn process_year_based(
        &mut self,
        thunderbird_dir: &str,
        output_mbox_path: &str,
        batch_size_years: u32,
    ) -> Result<ProcessingReport, Box<dyn std::error::Error>> {
        println!("Processing Thunderbird directory (year-based batches): {}", thunderbird_dir);
        println!("Batch size: {} years", batch_size_years);

        // TODO: Implement year-based batch processing
        // For now, fall back to single-pass
        eprintln!("Year-based processing not yet implemented, falling back to single-pass");
        self.process_single_pass(thunderbird_dir, output_mbox_path)
    }

    /// Refine threading by reading enhanced mbox and identifying missed connections
    fn refine_threading(
        &mut self,
        input_mbox_path: &str,
        output_mbox_path: &str,
    ) -> Result<ThreadingRefinementReport, Box<dyn std::error::Error>> {
        println!("Refining threading from: {}", input_mbox_path);

        // Parse the enhanced mbox file to extract threading metadata
        let parsed_emails = parse_mbox(input_mbox_path)?;
        println!("Read {} emails for threading refinement", parsed_emails.len());

        // Convert to threaded emails and extract existing threading metadata
        let threaded_emails = self.convert_to_threaded_emails(parsed_emails)?;

        // TODO: Extract existing threading metadata from X-headers
        // TODO: Identify potential missed threading connections
        // TODO: Apply additional threading passes

        // For now, just rewrite the file (this preserves all existing threading)
        let mut writer = EnhancedMboxWriter::new(output_mbox_path)?;
        let mut processed_count = 0u32;

        for email in &threaded_emails {
            if !email.is_duplicate {
                writer.write_email(email)?;
                processed_count += 1;
            }
        }

        writer.close()?;

        Ok(ThreadingRefinementReport {
            total_emails: threaded_emails.len() as u32,
            processed_emails: processed_count,
            threading_improvements: 0, // TODO: Track actual improvements
            error_count: 0,
            threads_created: self.threader.threads.len() as u32,
            cases_created: self.threader.cases.len() as u32,
        })
    }

    /// Find all Thunderbird mbox files in a directory
    fn find_thunderbird_files(&self, dir_path: &str) -> Result<Vec<(PathBuf, FolderType)>, io::Error> {
        let mut files = Vec::new();
        self.scan_directory(Path::new(dir_path), &mut files)?;
        Ok(files)
    }

    /// Recursively scan directory for Thunderbird files
    fn scan_directory(&self, dir: &Path, files: &mut Vec<(PathBuf, FolderType)>) -> Result<(), io::Error> {
        if !dir.is_dir() {
            return Ok(());
        }

        for entry in fs::read_dir(dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_dir() {
                // Recursively scan subdirectories
                self.scan_directory(&path, files)?;
            } else if self.is_thunderbird_mbox_file(&path) {
                let folder_type = self.determine_folder_type(&path);
                files.push((path, folder_type));
            }
        }
        
        Ok(())
    }

    /// Check if a file is a Thunderbird mbox file
    fn is_thunderbird_mbox_file(&self, path: &Path) -> bool {
        // Use existing detection logic from lib.rs
        if let Some(path_str) = path.to_str() {
            crate::is_thunderbird_mbox(path_str)
        } else {
            false
        }
    }

    /// Determine folder type from path
    fn determine_folder_type(&self, path: &Path) -> FolderType {
        let path_str = path.to_string_lossy().to_lowercase();
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .unwrap_or("")
            .to_lowercase();
        
        if file_name == "inbox" || path_str.contains("inbox") {
            FolderType::Inbox
        } else if file_name == "sent" || path_str.contains("sent") {
            FolderType::Sent
        } else if file_name == "drafts" || path_str.contains("drafts") {
            FolderType::Drafts
        } else if file_name == "trash" || path_str.contains("trash") {
            FolderType::Trash
        } else {
            FolderType::Other(file_name.to_string())
        }
    }



    /// Process a single mbox file with streaming output to avoid memory accumulation
    fn process_single_file_streaming(
        &mut self,
        file_path: &Path,
        _folder_type: FolderType,
        writer: &mut EnhancedMboxWriter
    ) -> Result<u32, Box<dyn std::error::Error>> {
        let path_str = file_path.to_str()
            .ok_or("Invalid file path")?;

        // Parse emails from the file
        let parsed_emails = parse_mbox(path_str)?;
        println!("  Parsed {} emails from {}", parsed_emails.len(), file_path.display());

        // Convert to threaded emails
        let mut threaded_emails = self.convert_to_threaded_emails(parsed_emails)?;

        // Set folder information for each email
        let folder_path_str = file_path.to_string_lossy();
        for email in &mut threaded_emails {
            email.folder_path = Some(folder_path_str.to_string());
            email.email_type = EmailThreader::detect_email_type(&folder_path_str, &email.from, &email.to);
            email.weight = email.email_type.default_weight();
        }

        // Sort emails by date for better threading
        threaded_emails.sort_by(|a, b| {
            a.sent_date.unwrap_or_else(|| Utc::now())
                .cmp(&b.sent_date.unwrap_or_else(|| Utc::now()))
        });

        // Apply threading, case cataloging, and duplicate detection
        self.apply_threading(&mut threaded_emails)?;
        self.apply_case_cataloging(&mut threaded_emails)?;
        self.detect_duplicates(&mut threaded_emails)?;

        // Write emails immediately to file
        let mut file_processed_count = 0u32;
        for email in &threaded_emails {
            if !email.is_duplicate {
                writer.write_email(email)?;
                self.processed_count += 1;
                file_processed_count += 1;
            } else {
                self.duplicate_count += 1;
            }
        }

        Ok(file_processed_count)
    }

    /// Convert ParsedEmail to ThreadedEmail with enhanced metadata
    fn convert_to_threaded_emails(&self, parsed_emails: Vec<ParsedEmail>) -> Result<Vec<ThreadedEmail>, Box<dyn std::error::Error>> {
        let mut threaded_emails = Vec::new();

        for parsed in parsed_emails {
            // Extract threading headers using mail-parser with error handling
            let (message_id, in_reply_to, references) = if let Some(raw_body) = &parsed.plain_text_body_raw {
                match self.extract_threading_headers(raw_body) {
                    Ok(headers) => headers,
                    Err(e) => {
                        eprintln!("Warning: Failed to extract headers for email {}: {}", parsed.id, e);
                        (None, Vec::new(), Vec::new())
                    }
                }
            } else {
                (None, Vec::new(), Vec::new())
            };

            let normalized_subject = parsed.subject.as_ref()
                .map(|s| EmailThreader::normalize_subject(s));

            // Determine email type from folder path (will be set later) and content
            let email_type = EmailThreader::detect_email_type(
                "", // Will be updated when we process files
                &parsed.from,
                &parsed.to
            );

            let threaded = ThreadedEmail {
                id: parsed.id,
                subject: parsed.subject,
                from: parsed.from,
                to: parsed.to,
                sent_date: parsed.sent_date,
                plain_text_body_raw: parsed.plain_text_body_raw,
                html_body_raw: parsed.html_body_raw,
                cleaned_plain_text_body: parsed.cleaned_plain_text_body,
                message_id,
                in_reply_to,
                references,
                thread_id: None,
                conversation_id: None,
                thread_position: None,
                email_type: email_type.clone(),
                weight: email_type.default_weight(),
                folder_path: None, // Will be set based on source file
                content_hash: None,
                normalized_subject,
                is_duplicate: false,
                duplicate_of: None,
                processing_notes: Vec::new(),

                // Initialize case cataloging fields
                case_id: None,
                case_subject: None,
                case_participants: Vec::new(),
            };

            threaded_emails.push(threaded);
        }

        Ok(threaded_emails)
    }

    /// Extract threading headers from email content
    fn extract_threading_headers(&self, content: &str) -> Result<(Option<String>, Vec<String>, Vec<String>), Box<dyn std::error::Error>> {
        // Try to parse the email content to extract headers
        // If parsing fails, we'll return empty values rather than failing completely
        let message = match MessageParser::default().parse(content.as_bytes()) {
            Some(msg) => msg,
            None => {
                // If parsing fails, try to extract headers manually from the raw content
                return self.extract_headers_manually(content);
            }
        };

        let message_id = message.message_id().map(|id| id.to_string());

        // Extract In-Reply-To headers
        let mut in_reply_to = Vec::new();
        for header_value in message.header_values("In-Reply-To") {
            if let HeaderValue::Text(text) = header_value {
                in_reply_to.push(text.to_string());
            }
        }

        // Extract References headers
        let mut references = Vec::new();
        for header_value in message.header_values("References") {
            if let HeaderValue::Text(text) = header_value {
                // References can contain multiple message IDs separated by spaces
                for reference in text.split_whitespace() {
                    references.push(reference.to_string());
                }
            }
        }

        Ok((message_id, in_reply_to, references))
    }

    /// Fallback method to extract headers manually when mail-parser fails
    fn extract_headers_manually(&self, content: &str) -> Result<(Option<String>, Vec<String>, Vec<String>), Box<dyn std::error::Error>> {
        let mut message_id = None;
        let mut in_reply_to = Vec::new();
        let mut references = Vec::new();

        // Look for headers in the first part of the content
        let lines: Vec<&str> = content.lines().take(100).collect(); // Check first 100 lines

        for line in lines {
            let line = line.trim();

            // Extract Message-ID
            if line.starts_with("Message-ID:") || line.starts_with("Message-Id:") {
                if let Some(id) = line.split(':').nth(1) {
                    message_id = Some(id.trim().to_string());
                }
            }

            // Extract In-Reply-To
            if line.starts_with("In-Reply-To:") {
                if let Some(reply_to) = line.split(':').nth(1) {
                    in_reply_to.push(reply_to.trim().to_string());
                }
            }

            // Extract References
            if line.starts_with("References:") {
                if let Some(refs) = line.split(':').nth(1) {
                    for reference in refs.split_whitespace() {
                        references.push(reference.trim().to_string());
                    }
                }
            }

            // Stop at empty line (end of headers)
            if line.is_empty() {
                break;
            }
        }

        Ok((message_id, in_reply_to, references))
    }

    /// Apply threading to all emails
    fn apply_threading(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Applying email threading across combined Inbox and Sent emails...");

        for email in emails.iter_mut() {
            match self.threader.thread_email(email) {
                Ok(thread_id) => {
                    email.processing_notes.push(format!("Threaded into: {}", thread_id));
                }
                Err(e) => {
                    email.processing_notes.push(format!("Threading error: {}", e));
                }
            }
        }

        println!("Created {} conversation threads", self.threader.threads.len());
        Ok(())
    }

    /// Apply case cataloging to all emails
    fn apply_case_cataloging(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Applying case cataloging...");

        for email in emails.iter_mut() {
            match self.threader.catalog_email(email) {
                Ok(()) => {
                    if let Some(case_id) = &email.case_id {
                        email.processing_notes.push(format!("Cataloged into case: {}", case_id));
                    }
                }
                Err(e) => {
                    email.processing_notes.push(format!("Case cataloging error: {}", e));
                }
            }
        }

        println!("Created {} email cases", self.threader.cases.len());
        Ok(())
    }

    /// Detect and mark duplicate emails
    fn detect_duplicates(&mut self, emails: &mut [ThreadedEmail]) -> Result<(), Box<dyn std::error::Error>> {
        println!("Detecting duplicate emails...");
        
        for email in emails.iter_mut() {
            if self.duplicate_detector.check_duplicate(email) {
                email.processing_notes.push("Marked as duplicate".to_string());
            }
        }
        
        Ok(())
    }
}

/// Folder type classification
#[derive(Debug, Clone, PartialEq)]
pub enum FolderType {
    Inbox,
    Sent,
    Drafts,
    Trash,
    Other(String),
}

/// Processing report
#[derive(Debug)]
pub struct ProcessingReport {
    pub total_emails: u32,
    pub processed_emails: u32,
    pub duplicate_emails: u32,
    pub error_count: u32,
    pub threads_created: u32,
    pub cases_created: u32,
    pub output_file: String,
}

impl ProcessingReport {
    pub fn print_summary(&self) {
        println!("=== Processing Summary ===");
        println!("Total emails found: {}", self.total_emails);
        println!("Emails processed: {}", self.processed_emails);
        println!("Duplicates removed: {}", self.duplicate_emails);
        println!("Errors encountered: {}", self.error_count);
        println!("Conversation threads: {}", self.threads_created);
        println!("Email cases: {}", self.cases_created);
        println!("Output file: {}", self.output_file);
        println!("========================");
    }
}

/// Threading refinement report for two-pass processing
#[derive(Debug)]
pub struct ThreadingRefinementReport {
    pub total_emails: u32,
    pub processed_emails: u32,
    pub threading_improvements: u32,
    pub error_count: u32,
    pub threads_created: u32,
    pub cases_created: u32,
}


