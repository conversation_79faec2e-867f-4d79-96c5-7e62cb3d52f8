// AI-Assisted Email Response System - Ingestion Service Library
// Core functionality for email parsing, cleaning, and database operations

use std::fs;
use std::io;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use dotenv::dotenv;
use reqwest::Client;
use qdrant_client::{
    Qdrant,
    qdrant::{
        CreateCollection, Distance, VectorParams,
        PointStruct, SearchPoints, UpsertPoints, ScrollPoints, Value, WithPayloadSelector, WithVectorsSelector
    },
};

// Import email threading functionality
pub use crate::email_threading::{ThreadedEmail, EmailThreader, EmailType, ConversationThread, EmailCase};
use std::collections::HashMap;
use mail_parser::MessageParser;

// New modules for enhanced email processing
pub mod email_threading;
pub mod enhanced_mbox;
pub mod test_data;
pub mod thunderbird_processor;

// Email parsing and cleaning functions

/// Converts HTML content to plain text with proper formatting.
/// Uses html2text crate for robust HTML parsing and conversion.
pub fn html_to_plain_text(html: &str) -> String {
    html2text::from_read(html.as_bytes(), 80)
}

/// Strips email signatures and quoted content from email text.
/// Removes lines starting with '>' (quoted content) and content after signature markers.
pub fn strip_signatures_and_quotes(text: &str) -> String {
    let mut cleaned_lines = Vec::new();
    
    for line in text.lines() {
        // Check for common signature markers
        if line.trim() == "--" || 
           (line.trim().starts_with("-- ") && line.trim().len() < 50) ||
           line.trim().starts_with("---") ||
           line.contains("Sent from my") ||
           line.contains("Best regards") ||
           line.contains("Kind regards") {
            break; // Stop processing once we hit a signature
        }
        
        // Skip quoted content (lines starting with >)
        if line.starts_with('>') || line.starts_with(" >") {
            continue;
        }
        
        // Skip forwarded message headers
        if line.starts_with("-----Original Message-----") ||
           line.starts_with("From:") && cleaned_lines.is_empty() {
            continue;
        }
        
        cleaned_lines.push(line);
    }
    
    // Remove excessive whitespace at the end
    while let Some(last_line) = cleaned_lines.last() {
        if last_line.trim().is_empty() {
            cleaned_lines.pop();
        } else {
            break;
        }
    }
    
    cleaned_lines.join("\n")
}

// Email struct for parsed emails
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ParsedEmail {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
}

// Message struct for Qdrant storage (metadata stored as payload)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from_address: Option<String>,
    pub to_addresses: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
    pub file_path: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,

    // Enhanced threading metadata
    pub thread_id: Option<String>,
    pub conversation_id: Option<String>,
    pub thread_position: Option<u32>,
    pub email_weight: Option<f32>,
    pub email_type: Option<String>,
    pub is_duplicate: Option<bool>,

    // Message headers for threading
    pub message_id: Option<String>,
    pub in_reply_to: Vec<String>,
    pub references: Vec<String>,

    // Content analysis
    pub content_hash: Option<String>,
    pub normalized_subject: Option<String>,

    // Case cataloging metadata
    pub case_id: Option<String>,
    pub case_subject: Option<String>,
    pub case_participants: Vec<String>,

    // Country identification for case organization
    pub country_uuid: Option<String>,
}

// Qdrant search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub message: Message,
    pub score: f32,
}

impl From<ParsedEmail> for Message {
    fn from(parsed: ParsedEmail) -> Self {
        let now = Utc::now();
        Message {
            id: parsed.id,
            subject: parsed.subject,
            from_address: parsed.from,
            to_addresses: parsed.to,
            sent_date: parsed.sent_date,
            plain_text_body_raw: parsed.plain_text_body_raw,
            html_body_raw: parsed.html_body_raw,
            cleaned_plain_text_body: parsed.cleaned_plain_text_body,
            file_path: None,
            created_at: now,
            updated_at: now,

            // Initialize threading metadata as None for standard emails
            thread_id: None,
            conversation_id: None,
            thread_position: None,
            email_weight: None,
            email_type: None,
            is_duplicate: None,

            // Initialize message headers
            message_id: None,
            in_reply_to: Vec::new(),
            references: Vec::new(),

            // Initialize content analysis
            content_hash: None,
            normalized_subject: None,

            // Initialize case cataloging
            case_id: None,
            case_subject: None,
            case_participants: Vec::new(),

            // Initialize country identification
            country_uuid: None,
        }
    }
}

impl From<ParsedEmail> for ThreadedEmail {
    fn from(parsed: ParsedEmail) -> Self {
        ThreadedEmail {
            id: parsed.id,
            subject: parsed.subject.clone(),
            from: parsed.from.clone(),
            to: parsed.to.clone(),
            sent_date: parsed.sent_date,
            plain_text_body_raw: parsed.plain_text_body_raw.clone(),
            html_body_raw: parsed.html_body_raw.clone(),
            cleaned_plain_text_body: parsed.cleaned_plain_text_body.clone(),

            // Extract threading headers from raw headers if available
            message_id: None, // Will be extracted from headers
            in_reply_to: Vec::new(), // Will be extracted from headers
            references: Vec::new(), // Will be extracted from headers
            thread_id: None,
            conversation_id: None,
            thread_position: None,

            // Default email categorization
            email_type: EmailType::Unknown,
            weight: 1.0,
            folder_path: None,

            // Content analysis
            content_hash: None,
            normalized_subject: None,

            // Processing metadata
            is_duplicate: false,
            duplicate_of: None,
            processing_notes: Vec::new(),

            // Case cataloging
            case_id: None,
            case_subject: None,
            case_participants: Vec::new(),
        }
    }
}

impl From<ThreadedEmail> for Message {
    fn from(threaded: ThreadedEmail) -> Self {
        let now = Utc::now();
        Message {
            id: threaded.id,
            subject: threaded.subject,
            from_address: threaded.from,
            to_addresses: threaded.to,
            sent_date: threaded.sent_date,
            plain_text_body_raw: threaded.plain_text_body_raw,
            html_body_raw: threaded.html_body_raw,
            cleaned_plain_text_body: threaded.cleaned_plain_text_body,
            file_path: threaded.folder_path,
            created_at: now,
            updated_at: now,

            // Threading metadata from ThreadedEmail
            thread_id: threaded.thread_id,
            conversation_id: threaded.conversation_id,
            thread_position: threaded.thread_position,
            email_weight: Some(threaded.weight),
            email_type: Some(format!("{:?}", threaded.email_type).to_lowercase()),
            is_duplicate: Some(threaded.is_duplicate),

            // Message headers
            message_id: threaded.message_id,
            in_reply_to: threaded.in_reply_to,
            references: threaded.references,

            // Content analysis
            content_hash: threaded.content_hash,
            normalized_subject: threaded.normalized_subject,

            // Case cataloging
            case_id: threaded.case_id.map(|uuid| uuid.to_string()),
            case_subject: threaded.case_subject,
            case_participants: threaded.case_participants,

            // Country identification
            country_uuid: {
                // Extract country UUID from processing notes
                threaded.processing_notes.iter()
                    .find(|note| note.starts_with("Country identified:"))
                    .and_then(|note| {
                        // Extract UUID from note like "Country identified: US (UUID: abc-def-...)"
                        if let Some(start) = note.find("UUID: ") {
                            let uuid_part = &note[start + 6..];
                            if let Some(end) = uuid_part.find(')') {
                                Some(uuid_part[..end].to_string())
                            } else {
                                Some(uuid_part.to_string())
                            }
                        } else {
                            None
                        }
                    })
            },
        }
    }
}

/// Parses a single .eml file into a ParsedEmail struct.
/// Applies cleaning functions and handles errors.
pub fn parse_eml(file_path: &str) -> Result<ParsedEmail, io::Error> {
    let content = fs::read_to_string(file_path)?;
    parse_email_from_string(&content)
}

/// Parses an mbox file and returns a vector of ParsedEmail structs.
/// Uses binary-safe reading to handle different encodings and attachments.
pub fn parse_mbox(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    // Read file as bytes to preserve encoding
    let bytes = fs::read(file_path)?;
    let mut emails = Vec::new();
    let mut current_email_bytes = Vec::new();
    let mut in_email = false;
    let mut line_start = 0;
    let mut total_emails_found = 0;
    let mut parsing_errors = 0;

    // Process the mbox file line by line in bytes to preserve encoding
    for i in 0..bytes.len() {
        if bytes[i] == b'\n' || i == bytes.len() - 1 {
            let line_end = if i == bytes.len() - 1 { i + 1 } else { i };
            let line_bytes = &bytes[line_start..line_end];

            // Convert line to string for pattern matching (this is safe for ASCII patterns)
            let line_str = String::from_utf8_lossy(line_bytes);
            let line_str = line_str.trim_end_matches('\r'); // Handle CRLF

            // Check for mbox separator lines
            if (line_str.starts_with("From ") && line_str.contains("@")) ||
               (line_str.starts_with("From - ")) {
                // Process the previous email if we have one
                if in_email && !current_email_bytes.is_empty() {
                    total_emails_found += 1;
                    match parse_email_from_bytes(&current_email_bytes) {
                        Ok(parsed_email) => emails.push(parsed_email),
                        Err(e) => {
                            parsing_errors += 1;
                            eprintln!("Warning: Failed to parse email #{} in mbox: {}", total_emails_found, e);
                            // Continue processing other emails
                        }
                    }
                }
                // Start a new email (skip the "From " separator line)
                current_email_bytes.clear();
                in_email = true;
            } else if in_email {
                // Add the line bytes to current email, preserving original encoding
                current_email_bytes.extend_from_slice(line_bytes);
                if i < bytes.len() - 1 {
                    current_email_bytes.push(b'\n');
                }
            }

            line_start = i + 1;
        }
    }

    // Process the last email
    if in_email && !current_email_bytes.is_empty() {
        total_emails_found += 1;
        match parse_email_from_bytes(&current_email_bytes) {
            Ok(parsed_email) => emails.push(parsed_email),
            Err(e) => {
                parsing_errors += 1;
                eprintln!("Warning: Failed to parse last email #{} in mbox: {}", total_emails_found, e);
            }
        }
    }

    // Report parsing statistics
    if parsing_errors > 0 {
        println!("Parsing summary: {} emails found, {} successfully parsed, {} failed",
                 total_emails_found, emails.len(), parsing_errors);
    } else {
        println!("Successfully parsed {} emails from mbox file", emails.len());
    }

    Ok(emails)
}

/// Helper function to parse email from bytes using mail-parser crate
/// Extracts only text content, filtering out binary attachments and non-text parts
/// This preserves the original encoding and lets mail-parser handle character set detection
fn parse_email_from_bytes(content: &[u8]) -> Result<ParsedEmail, io::Error> {
    // Use panic-safe parsing for both initial parsing and content extraction
    parse_email_from_bytes_safe(content)
}

/// Helper function to parse email from string content using mail-parser crate
/// Extracts only text content, filtering out binary attachments and non-text parts
fn parse_email_from_string(content: &str) -> Result<ParsedEmail, io::Error> {
    // Use panic-safe parsing for both initial parsing and content extraction
    parse_email_from_bytes_safe(content.as_bytes())
}

/// Comprehensive panic-safe email parsing that protects both initial parsing and content extraction
fn parse_email_from_bytes_safe(content: &[u8]) -> Result<ParsedEmail, io::Error> {
    // Use catch_unwind to handle panics from mail-parser during initial parsing
    match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        // Parse the email using mail-parser with full encoding support
        let message = MessageParser::default()
            .parse(content)
            .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message"))?;

        // Use panic-safe content extraction
        parse_message_to_email_safe(message)
    })) {
        Ok(result) => result,
        Err(panic_info) => {
            // Convert panic to a recoverable error
            let panic_msg = if let Some(s) = panic_info.downcast_ref::<&str>() {
                s.to_string()
            } else if let Some(s) = panic_info.downcast_ref::<String>() {
                s.clone()
            } else {
                "Unknown panic in mail-parser during initial parsing".to_string()
            };

            eprintln!("Warning: mail-parser panic caught during initial parsing: {}", panic_msg);
            Err(io::Error::new(
                io::ErrorKind::InvalidData,
                format!("Email parsing failed due to mail-parser panic during initial parsing: {}", panic_msg)
            ))
        }
    }
}

/// Panic-safe wrapper for parse_message_to_email that catches mail-parser panics during content extraction
fn parse_message_to_email_safe(message: mail_parser::Message) -> Result<ParsedEmail, io::Error> {
    // Use catch_unwind to handle panics from mail-parser when processing malformed multipart emails
    match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        parse_message_to_email(message)
    })) {
        Ok(result) => result,
        Err(panic_info) => {
            // Convert panic to a recoverable error
            let panic_msg = if let Some(s) = panic_info.downcast_ref::<&str>() {
                s.to_string()
            } else if let Some(s) = panic_info.downcast_ref::<String>() {
                s.clone()
            } else {
                "Unknown panic in mail-parser".to_string()
            };

            eprintln!("Warning: mail-parser panic caught during content extraction: {}", panic_msg);
            Err(io::Error::new(
                io::ErrorKind::InvalidData,
                format!("Email parsing failed due to mail-parser panic during content extraction: {}", panic_msg)
            ))
        }
    }
}

/// Common function to extract data from a parsed mail-parser Message
fn parse_message_to_email(message: mail_parser::Message) -> Result<ParsedEmail, io::Error> {

    // Extract basic header information
    let subject = message.subject().map(|s| s.to_string());
    let from = message.from()
        .and_then(|addr_list| addr_list.first())
        .and_then(|addr| addr.address())
        .map(|s| s.to_string());

    let mut to = Vec::new();
    if let Some(to_addrs) = message.to() {
        for addr in to_addrs.iter() {
            if let Some(address) = addr.address() {
                to.push(address.to_string());
            }
        }
    }

    let sent_date = message.date().map(|dt| {
        // Convert mail-parser DateTime to chrono DateTime<Utc>
        DateTime::<Utc>::from_timestamp(dt.to_timestamp(), 0).unwrap_or_else(|| Utc::now())
    });

    // Extract text content only - this automatically excludes binary attachments
    let mut plain_text_parts = Vec::new();
    let mut html_parts = Vec::new();

    // Collect all text body parts (excludes attachments and binary content)
    for i in 0..message.text_body_count() {
        if let Some(text) = message.body_text(i) {
            plain_text_parts.push(text.to_string());
        }
    }

    // Collect all HTML body parts and convert to plain text
    for i in 0..message.html_body_count() {
        if let Some(html) = message.body_html(i) {
            html_parts.push(html.to_string());
        }
    }

    // Combine plain text parts
    let plain_text_body_raw = if !plain_text_parts.is_empty() {
        Some(plain_text_parts.join("\n\n"))
    } else {
        None
    };

    // Combine HTML parts
    let html_body_raw = if !html_parts.is_empty() {
        Some(html_parts.join("\n\n"))
    } else {
        None
    };

    // Create cleaned plain text body
    let cleaned_plain_text_body = if let Some(html) = &html_body_raw {
        // Convert HTML to plain text and clean it
        let plain_from_html = html_to_plain_text(html);
        Some(strip_signatures_and_quotes(&plain_from_html))
    } else if let Some(plain) = &plain_text_body_raw {
        // Clean the plain text
        Some(strip_signatures_and_quotes(plain))
    } else {
        None
    };

    Ok(ParsedEmail {
        id: Uuid::new_v4(),
        subject,
        from,
        to,
        sent_date,
        plain_text_body_raw,
        html_body_raw,
        cleaned_plain_text_body,
    })
}

/// Establishes a connection to Qdrant vector database.
pub async fn establish_connection() -> Result<Qdrant, Box<dyn std::error::Error + Send + Sync>> {
    dotenv().ok(); // Load .env file if present

    let qdrant_url = std::env::var("QDRANT_URL")
        .unwrap_or_else(|_| "http://localhost:6334".to_string());

    let client = Qdrant::from_url(&qdrant_url).build()?;
    Ok(client)
}

/// Sets up Qdrant collections for email storage.
pub async fn setup_collections(client: &Qdrant) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Check if collection exists, create if not
    match client.collection_info(collection_name).await {
        Ok(_) => {
            println!("✓ Collection '{}' already exists", collection_name);
        }
        Err(_) => {
            println!("Creating collection '{}'...", collection_name);

            let create_collection = CreateCollection {
                collection_name: collection_name.to_string(),
                vectors_config: Some(VectorParams {
                    size: 1024, // intfloat/e5-large-v2 dimension
                    distance: Distance::Cosine as i32,
                    ..Default::default()
                }.into()),
                ..Default::default()
            };

            client.create_collection(create_collection).await?;
            println!("✓ Collection '{}' created successfully", collection_name);

            // Note: Qdrant automatically creates indexes for payload fields as needed
            // The enhanced metadata fields will be indexed when first used in queries
            println!("✓ Collection '{}' is ready for enhanced metadata storage", collection_name);
        }
    }

    Ok(())
}

/// Inserts a message with embedding into Qdrant.
pub async fn insert_message_with_embedding(
    client: &Qdrant,
    message: &Message,
    embedding: Vec<f32>
) -> Result<Uuid, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Create payload with message metadata
    let mut payload = HashMap::new();
    payload.insert("id".to_string(), Value::from(message.id.to_string()));
    payload.insert("subject".to_string(), Value::from(message.subject.clone().unwrap_or_default()));
    payload.insert("from_address".to_string(), Value::from(message.from_address.clone().unwrap_or_default()));
    payload.insert("to_addresses".to_string(), Value::from(serde_json::to_string(&message.to_addresses)?));
    payload.insert("sent_date".to_string(), Value::from(message.sent_date.map(|d| d.to_rfc3339()).unwrap_or_default()));
    payload.insert("plain_text_body_raw".to_string(), Value::from(message.plain_text_body_raw.clone().unwrap_or_default()));
    payload.insert("html_body_raw".to_string(), Value::from(message.html_body_raw.clone().unwrap_or_default()));
    payload.insert("cleaned_plain_text_body".to_string(), Value::from(message.cleaned_plain_text_body.clone().unwrap_or_default()));
    payload.insert("file_path".to_string(), Value::from(message.file_path.clone().unwrap_or_default()));
    payload.insert("created_at".to_string(), Value::from(message.created_at.to_rfc3339()));
    payload.insert("updated_at".to_string(), Value::from(message.updated_at.to_rfc3339()));

    // Add threading metadata
    payload.insert("thread_id".to_string(), Value::from(message.thread_id.clone().unwrap_or_default()));
    payload.insert("conversation_id".to_string(), Value::from(message.conversation_id.clone().unwrap_or_default()));
    payload.insert("thread_position".to_string(), Value::from(message.thread_position.unwrap_or(0).to_string()));
    payload.insert("email_weight".to_string(), Value::from(message.email_weight.unwrap_or(1.0).to_string()));
    payload.insert("email_type".to_string(), Value::from(message.email_type.clone().unwrap_or_default()));
    payload.insert("is_duplicate".to_string(), Value::from(message.is_duplicate.unwrap_or(false).to_string()));

    // Add message headers
    payload.insert("message_id".to_string(), Value::from(message.message_id.clone().unwrap_or_default()));
    payload.insert("in_reply_to".to_string(), Value::from(serde_json::to_string(&message.in_reply_to)?));
    payload.insert("references".to_string(), Value::from(serde_json::to_string(&message.references)?));

    // Add content analysis
    payload.insert("content_hash".to_string(), Value::from(message.content_hash.clone().unwrap_or_default()));
    payload.insert("normalized_subject".to_string(), Value::from(message.normalized_subject.clone().unwrap_or_default()));

    // Add case cataloging metadata
    payload.insert("case_id".to_string(), Value::from(message.case_id.clone().unwrap_or_default()));
    payload.insert("case_subject".to_string(), Value::from(message.case_subject.clone().unwrap_or_default()));
    payload.insert("case_participants".to_string(), Value::from(serde_json::to_string(&message.case_participants)?));

    // Add country identification
    payload.insert("country_uuid".to_string(), Value::from(message.country_uuid.clone().unwrap_or_default()));

    // Create point with vector and payload
    let point = PointStruct::new(
        message.id.to_string(),
        embedding,
        payload,
    );

    // Upsert point to collection
    let upsert_request = UpsertPoints {
        collection_name: collection_name.to_string(),
        points: vec![point],
        ..Default::default()
    };
    client.upsert_points(upsert_request).await?;

    Ok(message.id)
}

/// Searches for similar messages using vector similarity.
pub async fn search_similar_messages(
    client: &Qdrant,
    query_embedding: Vec<f32>,
    limit: Option<u64>,
    score_threshold: Option<f32>
) -> Result<Vec<SearchResult>, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";
    let limit = limit.unwrap_or(10);

    let search_result = client.search_points(SearchPoints {
        collection_name: collection_name.to_string(),
        vector: query_embedding,
        limit,
        score_threshold,
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        with_vectors: Some(WithVectorsSelector {
            selector_options: Some(qdrant_client::qdrant::with_vectors_selector::SelectorOptions::Enable(false)),
        }),
        ..Default::default()
    }).await?;

    let mut results = Vec::new();
    for scored_point in search_result.result {
        let payload = scored_point.payload;
            let id_str = payload.get("id").unwrap().to_string();
            let subject_str = payload.get("subject").unwrap().to_string();
            let from_str = payload.get("from_address").unwrap().to_string();
            let to_addresses_str = payload.get("to_addresses").unwrap().to_string();
            let sent_date_str = payload.get("sent_date").unwrap().to_string();
            let plain_text_str = payload.get("plain_text_body_raw").unwrap().to_string();
            let html_str = payload.get("html_body_raw").unwrap().to_string();
            let cleaned_str = payload.get("cleaned_plain_text_body").unwrap().to_string();
            let file_path_str = payload.get("file_path").unwrap().to_string();
            let created_at_str = payload.get("created_at").unwrap().to_string();
            let updated_at_str = payload.get("updated_at").unwrap().to_string();

            // Extract threading metadata with safe defaults
            let thread_id_str = payload.get("thread_id").map(|v| v.to_string()).unwrap_or_default();
            let conversation_id_str = payload.get("conversation_id").map(|v| v.to_string()).unwrap_or_default();
            let thread_position_str = payload.get("thread_position").map(|v| v.to_string()).unwrap_or_default();
            let email_weight_str = payload.get("email_weight").map(|v| v.to_string()).unwrap_or_default();
            let email_type_str = payload.get("email_type").map(|v| v.to_string()).unwrap_or_default();
            let is_duplicate_str = payload.get("is_duplicate").map(|v| v.to_string()).unwrap_or_default();

            // Extract message headers
            let message_id_str = payload.get("message_id").map(|v| v.to_string()).unwrap_or_default();
            let in_reply_to_str = payload.get("in_reply_to").map(|v| v.to_string()).unwrap_or_default();
            let references_str = payload.get("references").map(|v| v.to_string()).unwrap_or_default();

            // Extract content analysis
            let content_hash_str = payload.get("content_hash").map(|v| v.to_string()).unwrap_or_default();
            let normalized_subject_str = payload.get("normalized_subject").map(|v| v.to_string()).unwrap_or_default();

            // Extract case cataloging
            let case_id_str = payload.get("case_id").map(|v| v.to_string()).unwrap_or_default();
            let case_subject_str = payload.get("case_subject").map(|v| v.to_string()).unwrap_or_default();
            let case_participants_str = payload.get("case_participants").map(|v| v.to_string()).unwrap_or_default();

            // Extract country identification
            let country_uuid_str = payload.get("country_uuid").map(|v| v.to_string()).unwrap_or_default();

            let message = Message {
                id: Uuid::parse_str(&id_str.trim_matches('"'))?,
                subject: Some(subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                from_address: Some(from_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                to_addresses: safe_parse_json_array(&to_addresses_str)?,
                sent_date: Some(sent_date_str.trim_matches('"'))
                    .filter(|s| !s.is_empty())
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                plain_text_body_raw: Some(plain_text_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                html_body_raw: Some(html_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                cleaned_plain_text_body: Some(cleaned_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                file_path: Some(file_path_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                created_at: DateTime::parse_from_rfc3339(&created_at_str.trim_matches('"'))?.with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&updated_at_str.trim_matches('"'))?.with_timezone(&Utc),

                // Threading metadata
                thread_id: Some(thread_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                conversation_id: Some(conversation_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                thread_position: thread_position_str.trim_matches('"').parse::<u32>().ok(),
                email_weight: email_weight_str.trim_matches('"').parse::<f32>().ok(),
                email_type: Some(email_type_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                is_duplicate: is_duplicate_str.trim_matches('"').parse::<bool>().ok(),

                // Message headers
                message_id: Some(message_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                in_reply_to: safe_parse_json_array(&in_reply_to_str).unwrap_or_default(),
                references: safe_parse_json_array(&references_str).unwrap_or_default(),

                // Content analysis
                content_hash: Some(content_hash_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                normalized_subject: Some(normalized_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),

                // Case cataloging
                case_id: Some(case_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                case_subject: Some(case_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                case_participants: safe_parse_json_array(&case_participants_str).unwrap_or_default(),

                // Country identification
                country_uuid: Some(country_uuid_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
            };

        results.push(SearchResult {
            message,
            score: scored_point.score,
        });
    }

    Ok(results)
}

/// Gets recent messages (without vector search).
/// When no limit is specified, returns last 200 emails (prioritizing received emails).
pub async fn get_recent_messages(
    client: &Qdrant,
    limit: Option<u64>
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Fetch more messages than needed to allow filtering and sorting
    let mut all_messages = Vec::new();
    let mut offset = None;
    let target_limit = limit.unwrap_or(200); // Default to 200 emails
    let fetch_limit = if limit.is_none() { 1000 } else { target_limit }; // Fetch more to filter

    loop {
        let scroll_request = ScrollPoints {
            collection_name: collection_name.to_string(),
            limit: Some(std::cmp::min(fetch_limit, 1000) as u32), // Batch size
            offset,
            with_payload: Some(WithPayloadSelector {
                selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
            }),
            with_vectors: Some(WithVectorsSelector {
                selector_options: Some(qdrant_client::qdrant::with_vectors_selector::SelectorOptions::Enable(false)),
            }),
            ..Default::default()
        };
        let scroll_result = client.scroll(scroll_request).await?;

        for point in scroll_result.result {
            let payload = point.payload;
            let id_str = payload.get("id").unwrap().to_string();
            let subject_str = payload.get("subject").unwrap().to_string();
            let from_str = payload.get("from_address").unwrap().to_string();
            let to_addresses_str = payload.get("to_addresses").unwrap().to_string();
            let sent_date_str = payload.get("sent_date").unwrap().to_string();
            let plain_text_str = payload.get("plain_text_body_raw").unwrap().to_string();
            let html_str = payload.get("html_body_raw").unwrap().to_string();
            let cleaned_str = payload.get("cleaned_plain_text_body").unwrap().to_string();
            let file_path_str = payload.get("file_path").unwrap().to_string();
            let created_at_str = payload.get("created_at").unwrap().to_string();
            let updated_at_str = payload.get("updated_at").unwrap().to_string();

            // Extract threading metadata with safe defaults
            let thread_id_str = payload.get("thread_id").map(|v| v.to_string()).unwrap_or_default();
            let conversation_id_str = payload.get("conversation_id").map(|v| v.to_string()).unwrap_or_default();
            let thread_position_str = payload.get("thread_position").map(|v| v.to_string()).unwrap_or_default();
            let email_weight_str = payload.get("email_weight").map(|v| v.to_string()).unwrap_or_default();
            let email_type_str = payload.get("email_type").map(|v| v.to_string()).unwrap_or_default();
            let is_duplicate_str = payload.get("is_duplicate").map(|v| v.to_string()).unwrap_or_default();

            // Extract message headers
            let message_id_str = payload.get("message_id").map(|v| v.to_string()).unwrap_or_default();
            let in_reply_to_str = payload.get("in_reply_to").map(|v| v.to_string()).unwrap_or_default();
            let references_str = payload.get("references").map(|v| v.to_string()).unwrap_or_default();

            // Extract content analysis
            let content_hash_str = payload.get("content_hash").map(|v| v.to_string()).unwrap_or_default();
            let normalized_subject_str = payload.get("normalized_subject").map(|v| v.to_string()).unwrap_or_default();

            // Extract case cataloging
            let case_id_str = payload.get("case_id").map(|v| v.to_string()).unwrap_or_default();
            let case_subject_str = payload.get("case_subject").map(|v| v.to_string()).unwrap_or_default();
            let case_participants_str = payload.get("case_participants").map(|v| v.to_string()).unwrap_or_default();

            // Extract country identification
            let country_uuid_str = payload.get("country_uuid").map(|v| v.to_string()).unwrap_or_default();

            let message = Message {
                id: Uuid::parse_str(&id_str.trim_matches('"'))?,
                subject: Some(subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                from_address: Some(from_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                to_addresses: safe_parse_json_array(&to_addresses_str)?,
                sent_date: Some(sent_date_str.trim_matches('"'))
                    .filter(|s| !s.is_empty())
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                plain_text_body_raw: Some(plain_text_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                html_body_raw: Some(html_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                cleaned_plain_text_body: Some(cleaned_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                file_path: Some(file_path_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                created_at: DateTime::parse_from_rfc3339(&created_at_str.trim_matches('"'))?.with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&updated_at_str.trim_matches('"'))?.with_timezone(&Utc),

                // Threading metadata
                thread_id: Some(thread_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                conversation_id: Some(conversation_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                thread_position: thread_position_str.trim_matches('"').parse::<u32>().ok(),
                email_weight: email_weight_str.trim_matches('"').parse::<f32>().ok(),
                email_type: Some(email_type_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                is_duplicate: is_duplicate_str.trim_matches('"').parse::<bool>().ok(),

                // Message headers
                message_id: Some(message_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                in_reply_to: safe_parse_json_array(&in_reply_to_str).unwrap_or_default(),
                references: safe_parse_json_array(&references_str).unwrap_or_default(),

                // Content analysis
                content_hash: Some(content_hash_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                normalized_subject: Some(normalized_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),

                // Case cataloging
                case_id: Some(case_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                case_subject: Some(case_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                case_participants: safe_parse_json_array(&case_participants_str).unwrap_or_default(),

                // Country identification
                country_uuid: Some(country_uuid_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
            };

            // Collect all messages for now
            all_messages.push(message);
        }

        // Check if we should continue fetching
        if all_messages.len() >= fetch_limit as usize {
            break; // We have enough messages for processing
        }

        // Check if there are more results
        if let Some(next_offset) = scroll_result.next_page_offset {
            offset = Some(next_offset);
        } else {
            break; // No more results
        }
    }

    // Post-process the messages when no specific limit is provided
    if limit.is_none() {
        // Sort by date (newest first) and return the most recent emails
        all_messages.sort_by(|a, b| {
            let date_a = a.sent_date.unwrap_or(a.created_at);
            let date_b = b.sent_date.unwrap_or(b.created_at);
            date_b.cmp(&date_a) // Newest first
        });

        // Return the most recent emails (no filtering for now)
        all_messages.truncate(target_limit as usize);
        Ok(all_messages)
    } else {
        // For specific limits, return as-is (original behavior)
        all_messages.truncate(target_limit as usize);
        Ok(all_messages)
    }
}

/// Processes a single email file, generates embedding, and stores in Qdrant.
pub async fn ingest_email_file_with_embeddings(
    client: &Qdrant,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Uuid, Box<dyn std::error::Error + Send + Sync>> {
    let parsed_email = if file_path.ends_with(".mbox") {
        // For mbox files, take the first email for now
        let emails = parse_mbox(file_path)?;
        if emails.is_empty() {
            return Err("No emails found in mbox file".into());
        }
        emails.into_iter().next().unwrap()
    } else {
        parse_eml(file_path)?
    };

    let mut message = Message::from(parsed_email);
    message.file_path = Some(file_path.to_string());

    // Generate embedding for the cleaned text
    if let Some(cleaned_text) = &message.cleaned_plain_text_body {
        if !cleaned_text.trim().is_empty() {
            let embedding = get_embedding(cleaned_text, embedding_service_url).await?;
            let message_id = insert_message_with_embedding(client, &message, embedding).await?;
            println!("✓ Ingested email with embedding: {} (ID: {})", file_path, message_id);
            Ok(message_id)
        } else {
            Err("No cleaned text available for embedding".into())
        }
    } else {
        Err("No cleaned text available for embedding".into())
    }
}

/// Processes all emails from an mbox file with embeddings.
pub async fn ingest_mbox_file_with_embeddings(
    client: &Qdrant,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Vec<Uuid>, Box<dyn std::error::Error + Send + Sync>> {
    let emails = parse_mbox(file_path)?;
    let mut ingested_ids = Vec::new();

    for parsed_email in emails {
        let mut message = Message::from(parsed_email);
        message.file_path = Some(file_path.to_string());

        if let Some(cleaned_text) = &message.cleaned_plain_text_body {
            if !cleaned_text.trim().is_empty() {
                match get_embedding(cleaned_text, embedding_service_url).await {
                    Ok(embedding) => {
                        match insert_message_with_embedding(client, &message, embedding).await {
                            Ok(id) => {
                                ingested_ids.push(id);
                                println!("✓ Ingested email from mbox: {}", id);
                            }
                            Err(e) => eprintln!("Warning: Failed to insert email: {}", e),
                        }
                    }
                    Err(e) => eprintln!("Warning: Failed to generate embedding: {}", e),
                }
            } else {
                println!("⚠ Skipping email with empty text");
            }
        } else {
            println!("⚠ Skipping email with no cleaned text");
        }
    }

    println!("✓ Ingested {} emails from mbox: {}", ingested_ids.len(), file_path);
    Ok(ingested_ids)
}

/// Processes all emails from an enhanced mbox file with threading metadata and weighted embeddings.
pub async fn ingest_enhanced_mbox_file_with_embeddings(
    client: &Qdrant,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Vec<Uuid>, Box<dyn std::error::Error + Send + Sync>> {
    println!("Processing enhanced mbox file with threading: {}", file_path);

    // Parse emails from mbox file
    let parsed_emails = parse_mbox(file_path)?;
    println!("Parsed {} emails from mbox file", parsed_emails.len());

    // Convert to ThreadedEmail format with header extraction
    let mut threaded_emails = process_emails_with_threading(parsed_emails, file_path).await?;

    // Apply email threading and case cataloguing
    let mut threader = EmailThreader::new();

    // Process each email for threading
    for email in &mut threaded_emails {
        // Generate content hash for duplicate detection
        if let Some(content) = &email.cleaned_plain_text_body {
            use sha2::{Sha256, Digest};
            let mut hasher = Sha256::new();
            hasher.update(content.as_bytes());
            email.content_hash = Some(format!("{:x}", hasher.finalize()));
        }

        // Generate normalized subject
        if let Some(subject) = &email.subject {
            email.normalized_subject = Some(normalize_subject(subject));
        }

        // Apply threading based on extracted headers
        if let Err(e) = threader.thread_email(email) {
            eprintln!("Warning: Threading failed for email {}: {}", email.id, e);
        }

        // Apply case cataloguing
        if let Err(e) = threader.catalog_email(email) {
            eprintln!("Warning: Case cataloguing failed for email {}: {}", email.id, e);
        }

        // Set email weight based on type
        email.weight = email.email_type.default_weight();
    }

    // Apply country identification to all emails
    for email in &mut threaded_emails {
        if let Some(country_code) = identify_country(email) {
            let country_uuid = generate_country_uuid(&country_code);
            // Store country information in the email for later use
            email.processing_notes.push(format!("Country identified: {} (UUID: {})", country_code, country_uuid));
        }
    }

    println!("Created {} conversation threads", threader.threads.len());
    println!("Created {} email cases", threader.cases.len());

    // Convert threaded emails to Message format and ingest
    let mut ingested_ids = Vec::new();

    for threaded_email in threaded_emails {
        let message = Message::from(threaded_email.clone());

        if let Some(cleaned_text) = &message.cleaned_plain_text_body {
            if !cleaned_text.trim().is_empty() {
                match get_embedding(cleaned_text, embedding_service_url).await {
                    Ok(embedding) => {
                        // Apply weighting to the embedding based on email type
                        let weight = threaded_email.weight;
                        let weighted_embedding: Vec<f32> = embedding.iter()
                            .map(|&x| x * weight)
                            .collect();

                        match insert_message_with_embedding(client, &message, weighted_embedding).await {
                            Ok(id) => {
                                ingested_ids.push(id);
                                println!("✓ Ingested threaded email: {} (type: {:?}, weight: {:.2})",
                                    id, threaded_email.email_type, weight);
                            }
                            Err(e) => eprintln!("Warning: Failed to insert email: {}", e),
                        }
                    }
                    Err(e) => eprintln!("Warning: Failed to generate embedding: {}", e),
                }
            } else {
                println!("⚠ Skipping email with empty text");
            }
        } else {
            println!("⚠ Skipping email with no cleaned text");
        }
    }

    println!("✓ Ingested {} threaded emails from mbox: {}", ingested_ids.len(), file_path);
    Ok(ingested_ids)
}

/// Searches for similar messages with thread-aware prioritization
pub async fn search_similar_messages_with_thread_priority(
    client: &Qdrant,
    query_embedding: Vec<f32>,
    limit: u64,
    thread_id: Option<String>,
    conversation_id: Option<String>,
    collection_name: &str
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    use qdrant_client::qdrant::{Filter, Condition, FieldCondition, Match};

    let mut search_request = SearchPoints {
        collection_name: collection_name.to_string(),
        vector: query_embedding,
        limit,
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        ..Default::default()
    };

    // If thread context is provided, create a filter to prioritize thread emails
    if let Some(tid) = thread_id {
        let thread_condition = Condition {
            condition_one_of: Some(qdrant_client::qdrant::condition::ConditionOneOf::Field(
                FieldCondition {
                    key: "thread_id".to_string(),
                    r#match: Some(Match {
                        match_value: Some(qdrant_client::qdrant::r#match::MatchValue::Keyword(tid)),
                    }),
                    ..Default::default()
                }
            )),
        };

        search_request.filter = Some(Filter {
            should: vec![thread_condition],
            ..Default::default()
        });
    } else if let Some(cid) = conversation_id {
        let conversation_condition = Condition {
            condition_one_of: Some(qdrant_client::qdrant::condition::ConditionOneOf::Field(
                FieldCondition {
                    key: "conversation_id".to_string(),
                    r#match: Some(Match {
                        match_value: Some(qdrant_client::qdrant::r#match::MatchValue::Keyword(cid)),
                    }),
                    ..Default::default()
                }
            )),
        };

        search_request.filter = Some(Filter {
            should: vec![conversation_condition],
            ..Default::default()
        });
    }

    let search_result = client.search_points(search_request).await?;
    let mut messages = Vec::new();

    for scored_point in search_result.result {
        let message = reconstruct_message_from_payload(scored_point.payload)?;
        messages.push(message);
    }

    Ok(messages)
}

/// Retrieves all emails in a conversation thread
pub async fn get_conversation_thread(
    client: &Qdrant,
    thread_id: String,
    collection_name: &str
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    use qdrant_client::qdrant::{Filter, Condition, FieldCondition, Match};

    let thread_condition = Condition {
        condition_one_of: Some(qdrant_client::qdrant::condition::ConditionOneOf::Field(
            FieldCondition {
                key: "thread_id".to_string(),
                r#match: Some(Match {
                    match_value: Some(qdrant_client::qdrant::r#match::MatchValue::Keyword(thread_id)),
                }),
                ..Default::default()
            }
        )),
    };

    let scroll_request = ScrollPoints {
        collection_name: collection_name.to_string(),
        filter: Some(Filter {
            must: vec![thread_condition],
            ..Default::default()
        }),
        limit: Some(100), // Reasonable limit for conversation threads
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        ..Default::default()
    };

    let scroll_result = client.scroll(scroll_request).await?;
    let mut messages = Vec::new();

    for point in scroll_result.result {
        let message = reconstruct_message_from_payload(point.payload)?;
        messages.push(message);
    }

    // Sort by thread position and sent date
    messages.sort_by(|a, b| {
        match (a.thread_position, b.thread_position) {
            (Some(pos_a), Some(pos_b)) => pos_a.cmp(&pos_b),
            (Some(_), None) => std::cmp::Ordering::Less,
            (None, Some(_)) => std::cmp::Ordering::Greater,
            (None, None) => {
                match (&a.sent_date, &b.sent_date) {
                    (Some(date_a), Some(date_b)) => date_a.cmp(date_b),
                    (Some(_), None) => std::cmp::Ordering::Less,
                    (None, Some(_)) => std::cmp::Ordering::Greater,
                    (None, None) => std::cmp::Ordering::Equal,
                }
            }
        }
    });

    Ok(messages)
}

/// Searches for emails within a specific case
pub async fn search_emails_by_case(
    client: &Qdrant,
    case_id: String,
    collection_name: &str
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    use qdrant_client::qdrant::{Filter, Condition, FieldCondition, Match};

    let case_condition = Condition {
        condition_one_of: Some(qdrant_client::qdrant::condition::ConditionOneOf::Field(
            FieldCondition {
                key: "case_id".to_string(),
                r#match: Some(Match {
                    match_value: Some(qdrant_client::qdrant::r#match::MatchValue::Keyword(case_id)),
                }),
                ..Default::default()
            }
        )),
    };

    let scroll_request = ScrollPoints {
        collection_name: collection_name.to_string(),
        filter: Some(Filter {
            must: vec![case_condition],
            ..Default::default()
        }),
        limit: Some(1000), // Higher limit for case emails
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        ..Default::default()
    };

    let scroll_result = client.scroll(scroll_request).await?;
    let mut messages = Vec::new();

    for point in scroll_result.result {
        let message = reconstruct_message_from_payload(point.payload)?;
        messages.push(message);
    }

    // Sort by sent date (newest first)
    messages.sort_by(|a, b| {
        match (&b.sent_date, &a.sent_date) {
            (Some(date_b), Some(date_a)) => date_b.cmp(date_a),
            (Some(_), None) => std::cmp::Ordering::Less,
            (None, Some(_)) => std::cmp::Ordering::Greater,
            (None, None) => std::cmp::Ordering::Equal,
        }
    });

    Ok(messages)
}

/// Helper function to reconstruct Message from Qdrant payload
fn reconstruct_message_from_payload(
    payload: std::collections::HashMap<String, Value>
) -> Result<Message, Box<dyn std::error::Error + Send + Sync>> {
    // This function reuses the same logic as in search_similar_messages and get_recent_messages
    // Extract basic fields
    let id_str = payload.get("id").map(|v| v.to_string()).unwrap_or_default();
    let subject_str = payload.get("subject").map(|v| v.to_string()).unwrap_or_default();
    let from_str = payload.get("from_address").map(|v| v.to_string()).unwrap_or_default();
    let to_addresses_str = payload.get("to_addresses").map(|v| v.to_string()).unwrap_or_default();
    let sent_date_str = payload.get("sent_date").map(|v| v.to_string()).unwrap_or_default();
    let plain_text_str = payload.get("plain_text_body_raw").map(|v| v.to_string()).unwrap_or_default();
    let html_str = payload.get("html_body_raw").map(|v| v.to_string()).unwrap_or_default();
    let cleaned_str = payload.get("cleaned_plain_text_body").map(|v| v.to_string()).unwrap_or_default();
    let file_path_str = payload.get("file_path").map(|v| v.to_string()).unwrap_or_default();
    let created_at_str = payload.get("created_at").map(|v| v.to_string()).unwrap_or_default();
    let updated_at_str = payload.get("updated_at").map(|v| v.to_string()).unwrap_or_default();

    // Extract threading metadata with safe defaults
    let thread_id_str = payload.get("thread_id").map(|v| v.to_string()).unwrap_or_default();
    let conversation_id_str = payload.get("conversation_id").map(|v| v.to_string()).unwrap_or_default();
    let thread_position_str = payload.get("thread_position").map(|v| v.to_string()).unwrap_or_default();
    let email_weight_str = payload.get("email_weight").map(|v| v.to_string()).unwrap_or_default();
    let email_type_str = payload.get("email_type").map(|v| v.to_string()).unwrap_or_default();
    let is_duplicate_str = payload.get("is_duplicate").map(|v| v.to_string()).unwrap_or_default();

    // Extract message headers
    let message_id_str = payload.get("message_id").map(|v| v.to_string()).unwrap_or_default();
    let in_reply_to_str = payload.get("in_reply_to").map(|v| v.to_string()).unwrap_or_default();
    let references_str = payload.get("references").map(|v| v.to_string()).unwrap_or_default();

    // Extract content analysis
    let content_hash_str = payload.get("content_hash").map(|v| v.to_string()).unwrap_or_default();
    let normalized_subject_str = payload.get("normalized_subject").map(|v| v.to_string()).unwrap_or_default();

    // Extract case cataloging
    let case_id_str = payload.get("case_id").map(|v| v.to_string()).unwrap_or_default();
    let case_subject_str = payload.get("case_subject").map(|v| v.to_string()).unwrap_or_default();
    let case_participants_str = payload.get("case_participants").map(|v| v.to_string()).unwrap_or_default();

    // Extract country identification
    let country_uuid_str = payload.get("country_uuid").map(|v| v.to_string()).unwrap_or_default();

    let message = Message {
        id: Uuid::parse_str(&id_str.trim_matches('"'))?,
        subject: Some(subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        from_address: Some(from_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        to_addresses: safe_parse_json_array(&to_addresses_str)?,
        sent_date: Some(sent_date_str.trim_matches('"'))
            .filter(|s| !s.is_empty())
            .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
            .map(|dt| dt.with_timezone(&Utc)),
        plain_text_body_raw: Some(plain_text_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        html_body_raw: Some(html_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        cleaned_plain_text_body: Some(cleaned_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        file_path: Some(file_path_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        created_at: DateTime::parse_from_rfc3339(&created_at_str.trim_matches('"'))?.with_timezone(&Utc),
        updated_at: DateTime::parse_from_rfc3339(&updated_at_str.trim_matches('"'))?.with_timezone(&Utc),

        // Threading metadata
        thread_id: Some(thread_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        conversation_id: Some(conversation_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        thread_position: thread_position_str.trim_matches('"').parse::<u32>().ok(),
        email_weight: email_weight_str.trim_matches('"').parse::<f32>().ok(),
        email_type: Some(email_type_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        is_duplicate: is_duplicate_str.trim_matches('"').parse::<bool>().ok(),

        // Message headers
        message_id: Some(message_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        in_reply_to: safe_parse_json_array(&in_reply_to_str).unwrap_or_default(),
        references: safe_parse_json_array(&references_str).unwrap_or_default(),

        // Content analysis
        content_hash: Some(content_hash_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        normalized_subject: Some(normalized_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),

        // Case cataloging
        case_id: Some(case_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        case_subject: Some(case_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        case_participants: safe_parse_json_array(&case_participants_str).unwrap_or_default(),

        // Country identification
        country_uuid: Some(country_uuid_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
    };

    Ok(message)
}

// Embedding service integration

#[derive(Debug, Serialize)]
struct EmbeddingRequest {
    text: String,
    normalize: Option<bool>,
}

#[derive(Debug, Deserialize)]
struct EmbeddingResponse {
    embedding: Vec<f32>,
    #[allow(dead_code)]
    model_name: String,
    #[allow(dead_code)]
    dimension: usize,
}

/// Detects if a file is a Thunderbird mbox file (no extension, contains email headers)
pub fn is_thunderbird_mbox(file_path: &str) -> bool {
    use std::path::Path;

    let path = Path::new(file_path);

    // Check if file has no extension
    if path.extension().is_some() {
        return false;
    }

    // Check if it's in a .sbd directory or has typical Thunderbird names
    let parent_dir = path.parent().map(|p| p.to_string_lossy().to_lowercase());
    let file_name = path.file_name().map(|n| n.to_string_lossy().to_lowercase());

    if let Some(parent) = parent_dir {
        if parent.contains(".sbd") {
            return true;
        }
    }

    // Check for common Thunderbird folder names
    if let Some(name) = file_name {
        if ["inbox", "sent", "drafts", "trash", "junk", "outbox", "templates"].contains(&name.as_str()) {
            return true;
        }
    }

    // Try to read first few lines to detect mbox format (handle binary data)
    if let Ok(bytes) = std::fs::read(file_path) {
        // Try to convert first 2KB to string, ignoring invalid UTF-8
        let preview_size = std::cmp::min(2048, bytes.len());
        let preview = String::from_utf8_lossy(&bytes[..preview_size]);
        let lines: Vec<&str> = preview.lines().take(10).collect();
        for line in lines {
            // Look for typical mbox "From " separator or Thunderbird "From - " separator
            if (line.starts_with("From ") && line.contains("@")) ||
               line.starts_with("From - ") {
                return true;
            }
            // Look for Mozilla/Thunderbird specific headers
            if line.starts_with("X-Mozilla-Status:") ||
               line.starts_with("X-Mozilla-Status2:") {
                return true;
            }
            // Look for standard email headers
            if line.starts_with("Return-Path:") ||
               line.starts_with("Delivered-To:") ||
               line.starts_with("Received:") ||
               line.starts_with("Message-ID:") {
                return true;
            }
        }
    }

    false
}

/// Helper function to safely parse JSON from Qdrant payload
fn safe_parse_json_array(json_str: &str) -> Result<Vec<String>, Box<dyn std::error::Error + Send + Sync>> {
    let trimmed = json_str.trim_matches('"');

    // Try parsing directly first
    if let Ok(result) = serde_json::from_str::<Vec<String>>(trimmed) {
        return Ok(result);
    }

    // If that fails, try parsing as a string and then parsing the inner content
    if let Ok(inner_str) = serde_json::from_str::<String>(trimmed) {
        if let Ok(result) = serde_json::from_str::<Vec<String>>(&inner_str) {
            return Ok(result);
        }
    }

    // Fallback: return empty array
    Ok(vec![])
}

/// Generates an embedding for the given text using the embedding service.
pub async fn get_embedding(text: &str, embedding_service_url: &str) -> Result<Vec<f32>, Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();

    let request = EmbeddingRequest {
        text: text.to_string(),
        normalize: Some(true),
    };

    let response = client
        .post(&format!("{}/embed", embedding_service_url))
        .json(&request)
        .send()
        .await?;

    if response.status().is_success() {
        let embedding_response: EmbeddingResponse = response.json().await?;
        Ok(embedding_response.embedding)
    } else {
        let error_text = response.text().await?;
        Err(format!("Embedding service error: {}", error_text).into())
    }
}

/// Extracts threading headers from raw email content
pub fn extract_threading_headers(raw_content: &str) -> (Option<String>, Vec<String>, Vec<String>) {
    let mut message_id = None;
    let mut in_reply_to = Vec::new();
    let mut references = Vec::new();

    // Parse headers line by line
    for line in raw_content.lines() {
        let line = line.trim();

        // Stop at empty line (end of headers)
        if line.is_empty() {
            break;
        }

        // Extract Message-ID
        if line.to_lowercase().starts_with("message-id:") {
            if let Some(id) = line.split(':').nth(1) {
                message_id = Some(id.trim().to_string());
            }
        }

        // Extract In-Reply-To
        if line.to_lowercase().starts_with("in-reply-to:") {
            if let Some(reply_to) = line.split(':').nth(1) {
                // Parse multiple message IDs separated by whitespace
                for id in reply_to.split_whitespace() {
                    if !id.trim().is_empty() {
                        in_reply_to.push(id.trim().to_string());
                    }
                }
            }
        }

        // Extract References
        if line.to_lowercase().starts_with("references:") {
            if let Some(refs) = line.split(':').nth(1) {
                // Parse multiple message IDs separated by whitespace
                for id in refs.split_whitespace() {
                    if !id.trim().is_empty() {
                        references.push(id.trim().to_string());
                    }
                }
            }
        }
    }

    (message_id, in_reply_to, references)
}

/// Enhanced function to process emails with threading and header extraction
pub async fn process_emails_with_threading(
    emails: Vec<ParsedEmail>,
    file_path: &str
) -> Result<Vec<ThreadedEmail>, Box<dyn std::error::Error + Send + Sync>> {
    let mut threaded_emails = Vec::new();

    for parsed_email in emails {
        let mut threaded_email = ThreadedEmail::from(parsed_email.clone());

        // Extract threading headers from raw content if available
        if let Some(raw_content) = &parsed_email.plain_text_body_raw {
            let (message_id, in_reply_to, references) = extract_threading_headers(raw_content);
            threaded_email.message_id = message_id;
            threaded_email.in_reply_to = in_reply_to;
            threaded_email.references = references;
        }

        // Set folder path
        threaded_email.folder_path = Some(file_path.to_string());

        // Determine email type from folder path
        threaded_email.email_type = if file_path.to_lowercase().contains("sent") {
            EmailType::Sent
        } else if file_path.to_lowercase().contains("draft") {
            EmailType::Draft
        } else {
            EmailType::Received
        };

        threaded_emails.push(threaded_email);
    }

    Ok(threaded_emails)
}

/// Normalizes email subject by removing common prefixes and cleaning up
pub fn normalize_subject(subject: &str) -> String {
    let mut normalized = subject.to_lowercase();

    // Remove common prefixes
    let prefixes = ["re:", "fwd:", "fw:", "[external]", "re[2]:", "re[3]:", "re[4]:", "re[5]:"];
    for prefix in &prefixes {
        if normalized.starts_with(prefix) {
            normalized = normalized[prefix.len()..].trim().to_string();
        }
    }

    // Remove extra whitespace
    normalized = normalized.split_whitespace().collect::<Vec<_>>().join(" ");

    // Remove common email artifacts
    normalized = normalized.replace("automatic reply:", "");
    normalized = normalized.replace("out of office:", "");

    normalized.trim().to_string()
}

/// Identifies the country associated with an email based on various indicators
pub fn identify_country(email: &ThreadedEmail) -> Option<String> {
    // Country identification based on email domains, content, and participants
    let mut country_indicators = Vec::new();

    // Check email domains for country indicators
    if let Some(from) = &email.from {
        if let Some(domain) = from.split('@').nth(1) {
            let country = match domain.to_lowercase().as_str() {
                d if d.ends_with(".uk") || d.ends_with(".co.uk") => Some("GB"),
                d if d.ends_with(".de") => Some("DE"),
                d if d.ends_with(".fr") => Some("FR"),
                d if d.ends_with(".it") => Some("IT"),
                d if d.ends_with(".es") => Some("ES"),
                d if d.ends_with(".nl") => Some("NL"),
                d if d.ends_with(".be") => Some("BE"),
                d if d.ends_with(".ch") => Some("CH"),
                d if d.ends_with(".at") => Some("AT"),
                d if d.ends_with(".se") => Some("SE"),
                d if d.ends_with(".no") => Some("NO"),
                d if d.ends_with(".dk") => Some("DK"),
                d if d.ends_with(".fi") => Some("FI"),
                d if d.ends_with(".pl") => Some("PL"),
                d if d.ends_with(".cz") => Some("CZ"),
                d if d.ends_with(".hu") => Some("HU"),
                d if d.ends_with(".ca") => Some("CA"),
                d if d.ends_with(".au") => Some("AU"),
                d if d.ends_with(".nz") => Some("NZ"),
                d if d.ends_with(".jp") => Some("JP"),
                d if d.ends_with(".kr") => Some("KR"),
                d if d.ends_with(".cn") => Some("CN"),
                d if d.ends_with(".in") => Some("IN"),
                d if d.ends_with(".br") => Some("BR"),
                d if d.ends_with(".mx") => Some("MX"),
                d if d.ends_with(".ar") => Some("AR"),
                d if d.ends_with(".cl") => Some("CL"),
                d if d.ends_with(".za") => Some("ZA"),
                d if d.ends_with(".sg") => Some("SG"),
                d if d.ends_with(".hk") => Some("HK"),
                d if d.ends_with(".tw") => Some("TW"),
                d if d.ends_with(".th") => Some("TH"),
                d if d.ends_with(".my") => Some("MY"),
                d if d.ends_with(".id") => Some("ID"),
                d if d.ends_with(".ph") => Some("PH"),
                d if d.ends_with(".vn") => Some("VN"),
                d if d.ends_with(".ru") => Some("RU"),
                d if d.ends_with(".ua") => Some("UA"),
                d if d.ends_with(".tr") => Some("TR"),
                d if d.ends_with(".il") => Some("IL"),
                d if d.ends_with(".ae") => Some("AE"),
                d if d.ends_with(".sa") => Some("SA"),
                d if d.ends_with(".eg") => Some("EG"),
                _ => {
                    // Default to US for .com, .org, .net, .edu, .gov domains
                    if domain.ends_with(".com") || domain.ends_with(".org") || domain.ends_with(".net") ||
                       domain.ends_with(".edu") || domain.ends_with(".gov") {
                        Some("US")
                    } else {
                        None
                    }
                }
            };
            if let Some(c) = country {
                country_indicators.push(c.to_string());
            }
        }
    }

    // Check recipient domains
    for to_addr in &email.to {
        if let Some(domain) = to_addr.split('@').nth(1) {
            if domain.to_lowercase().ends_with(".uk") || domain.to_lowercase().ends_with(".co.uk") {
                country_indicators.push("GB".to_string());
            } else if domain.to_lowercase().ends_with(".de") {
                country_indicators.push("DE".to_string());
            } else if domain.to_lowercase().ends_with(".fr") {
                country_indicators.push("FR".to_string());
            }
            // Add more domain checks as needed
        }
    }

    // Check email content for country/location keywords
    if let Some(content) = &email.cleaned_plain_text_body {
        let content_lower = content.to_lowercase();

        // European countries
        if content_lower.contains("united kingdom") || content_lower.contains("uk ") ||
           content_lower.contains("london") || content_lower.contains("manchester") ||
           content_lower.contains("birmingham") || content_lower.contains("glasgow") {
            country_indicators.push("GB".to_string());
        }
        if content_lower.contains("germany") || content_lower.contains("deutschland") ||
           content_lower.contains("berlin") || content_lower.contains("munich") ||
           content_lower.contains("hamburg") || content_lower.contains("frankfurt") {
            country_indicators.push("DE".to_string());
        }
        if content_lower.contains("france") || content_lower.contains("paris") ||
           content_lower.contains("lyon") || content_lower.contains("marseille") {
            country_indicators.push("FR".to_string());
        }

        // North America
        if content_lower.contains("united states") || content_lower.contains("usa") ||
           content_lower.contains("new york") || content_lower.contains("california") ||
           content_lower.contains("texas") || content_lower.contains("florida") {
            country_indicators.push("US".to_string());
        }
        if content_lower.contains("canada") || content_lower.contains("toronto") ||
           content_lower.contains("vancouver") || content_lower.contains("montreal") {
            country_indicators.push("CA".to_string());
        }

        // Add more content-based detection as needed
    }

    // Return the most common country indicator, or None if no clear indication
    if country_indicators.is_empty() {
        None
    } else {
        // Count occurrences and return the most frequent
        let mut counts = std::collections::HashMap::new();
        for country in country_indicators {
            *counts.entry(country).or_insert(0) += 1;
        }
        counts.into_iter().max_by_key(|(_, count)| *count).map(|(country, _)| country)
    }
}

/// Generates a country UUID based on the country code for consistent case organization
pub fn generate_country_uuid(country_code: &str) -> String {
    use sha2::{Sha256, Digest};
    let mut hasher = Sha256::new();
    hasher.update(format!("country:{}", country_code).as_bytes());
    let hash = hasher.finalize();

    // Create a UUID-like string from the hash
    let hash_str = format!("{:x}", hash);
    format!("{}-{}-{}-{}-{}",
        &hash_str[0..8],
        &hash_str[8..12],
        &hash_str[12..16],
        &hash_str[16..20],
        &hash_str[20..32]
    )
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_html_to_plain_text() {
        let html_content = "<html><body><h1>Hello</h1><p>This is <b>HTML</b> with a <a href=\"http://example.com\">link</a>.</p><ul><li>Item 1</li><li>Item 2</li></ul></body></html>";
        let plain_text = html_to_plain_text(html_content);
        assert!(plain_text.contains("Hello"));
        assert!(plain_text.contains("HTML"));
        assert!(plain_text.contains("Item 1"));
        assert!(plain_text.contains("Item 2"));
    }

    #[test]
    fn test_strip_signatures_and_quotes() {
        let text_with_signature = "Hello,\n\nThis is the main body.\n\n-- \nJohn Doe\nSoftware Engineer";
        let cleaned = strip_signatures_and_quotes(text_with_signature);
        assert_eq!(cleaned, "Hello,\n\nThis is the main body.");

        let text_with_quote = "Hello,\n\nMy reply.\n\n> On Mon, Jan 1, 2023 at 10:00 AM John Doe <<EMAIL>> wrote:\n> This is a quoted message.";
        let cleaned_quote = strip_signatures_and_quotes(text_with_quote);
        assert_eq!(cleaned_quote, "Hello,\n\nMy reply.");
    }

    #[test]
    fn test_parse_valid_eml() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Test Subject".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert_eq!(parsed_email.to, vec!["<EMAIL>".to_string()]);
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_mbox_single_email() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = "From <EMAIL> Mon Jan  1 10:00:00 2024\nFrom: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.\n\n";
        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 1);

        let email = &parsed_emails[0];
        assert_eq!(email.subject, Some("Test Subject".to_string()));
        assert_eq!(email.from, Some("<EMAIL>".to_string()));
        assert_eq!(email.to, vec!["<EMAIL>".to_string()]);
        assert!(email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_mbox_multiple_emails() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = "From <EMAIL> Mon Jan  1 10:00:00 2024\nFrom: <EMAIL>\nSubject: First Email\nTo: <EMAIL>\n\nFirst email content.\n\nFrom <EMAIL> Mon Jan  1 11:00:00 2024\nFrom: <EMAIL>\nSubject: Second Email\nTo: <EMAIL>\n\nSecond email content.\n\n";
        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 2);

        assert_eq!(parsed_emails[0].subject, Some("First Email".to_string()));
        assert_eq!(parsed_emails[0].from, Some("<EMAIL>".to_string()));

        assert_eq!(parsed_emails[1].subject, Some("Second Email".to_string()));
        assert_eq!(parsed_emails[1].from, Some("<EMAIL>".to_string()));
        Ok(())
    }

    #[test]
    fn test_parse_eml_with_attachments() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Email with Attachment\nTo: <EMAIL>\nContent-Type: multipart/mixed; boundary=\"boundary123\"\n\n--boundary123\nContent-Type: text/plain\n\nThis email has an attachment.\n\n--boundary123\nContent-Type: application/pdf\nContent-Disposition: attachment; filename=\"document.pdf\"\n\nPDF content here\n--boundary123--";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Email with Attachment".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_eml_html_content() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: HTML Email\nTo: <EMAIL>\nContent-Type: text/html\n\n<html><body><h1>Hello</h1><p>This is an HTML email.</p></body></html>";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("HTML Email".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Should extract plain text from HTML
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("Hello"));
            assert!(body.contains("This is an HTML email"));
            assert!(!body.contains("<html>"));
            assert!(!body.contains("<body>"));
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_missing_headers() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "Subject: Missing From Header\n\nThis email is missing the From header.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Missing From Header".to_string()));
        assert_eq!(parsed_email.from, None);
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_eml_unicode_content() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Unicode Test 🚀\nTo: <EMAIL>\n\nHello! This email contains unicode: 你好 🌟 café";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Unicode Test 🚀".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("你好"));
            assert!(body.contains("🌟"));
            assert!(body.contains("café"));
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_excludes_binary_attachments() -> Result<(), Box<dyn std::error::Error>> {
        // Create a multipart email with text content and binary attachment
        let eml_content = r#"From: <EMAIL>
Subject: Email with Binary Attachment
To: <EMAIL>
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain; charset="utf-8"

This is the actual email text content that should be extracted.

--boundary123
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="binary_file.bin"
Content-Transfer-Encoding: base64

iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU8
--boundary123
Content-Type: image/jpeg
Content-Disposition: attachment; filename="image.jpg"

BINARY_IMAGE_DATA_HERE_SHOULD_BE_EXCLUDED
--boundary123--"#;

        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Email with Binary Attachment".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Verify that only text content is extracted
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("This is the actual email text content"));
            // Verify binary content is NOT included
            assert!(!body.contains("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJ"));
            assert!(!body.contains("BINARY_IMAGE_DATA_HERE_SHOULD_BE_EXCLUDED"));
            assert!(!body.contains("base64"));
        } else {
            panic!("Expected cleaned_plain_text_body to contain text content");
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_greek_characters() -> Result<(), Box<dyn std::error::Error>> {
        // Create an email with Greek characters in various encodings
        let eml_content = r#"From: <EMAIL>
Subject: =?UTF-8?B?zp/Ouc66zr/Ovc6/zrzOuc66z4wgzrXOuc+CzrHOuc+EzrfPgc65zrE=?=
To: <EMAIL>
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: 8bit

Γεια σας! Αυτό είναι ένα email με ελληνικούς χαρακτήρες.

Περιεχόμενο:
• Κείμενο στα ελληνικά
• Ειδικοί χαρακτήρες: άλφα, βήτα, γάμμα
• Τόνοι και διαλυτικά: ά, έ, ή, ί, ό, ύ, ώ, ΐ, ΰ

Τέλος μηνύματος."#;

        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;

        // Check that the subject is properly decoded
        assert!(parsed_email.subject.is_some());
        let subject = parsed_email.subject.as_ref().unwrap();
        assert!(subject.contains("Οικονομικό"), "Subject should contain Greek text: {}", subject);

        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Verify that Greek characters are preserved in the body
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("Γεια σας"), "Body should contain 'Γεια σας': {}", body);
            assert!(body.contains("ελληνικούς χαρακτήρες"), "Body should contain 'ελληνικούς χαρακτήρες': {}", body);
            assert!(body.contains("άλφα, βήτα, γάμμα"), "Body should contain Greek letters: {}", body);
            assert!(body.contains("ά, έ, ή, ί, ό, ύ, ώ"), "Body should contain accented characters: {}", body);
            assert!(body.contains("Τέλος μηνύματος"), "Body should contain 'Τέλος μηνύματος': {}", body);
        } else {
            panic!("Expected cleaned_plain_text_body to contain Greek text");
        }
        Ok(())
    }

    #[test]
    fn test_parse_mbox_greek_characters() -> Result<(), Box<dyn std::error::Error>> {
        // Create an mbox file with Greek characters
        let mbox_content = r#"From <EMAIL> Mon Jan  1 10:00:00 2024
From: <EMAIL>
Subject: =?UTF-8?B?zp/Ouc66zr/Ovc6/zrzOuc66z4wgzrXOuc+CzrHOuc+EzrfPgc65zrE=?=
To: <EMAIL>
Content-Type: text/plain; charset="utf-8"

Γεια σας! Αυτό είναι ένα email με ελληνικούς χαρακτήρες στο mbox format.

"#;

        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 1);

        let email = &parsed_emails[0];
        assert!(email.subject.is_some());

        // Verify that Greek characters are preserved in the body
        if let Some(body) = &email.cleaned_plain_text_body {
            assert!(body.contains("Γεια σας"), "Body should contain 'Γεια σας': {}", body);
            assert!(body.contains("ελληνικούς χαρακτήρες"), "Body should contain 'ελληνικούς χαρακτήρες': {}", body);
        } else {
            panic!("Expected cleaned_plain_text_body to contain Greek text");
        }
        Ok(())
    }

    #[test]
    fn test_parse_invalid_file_path() {
        let result = parse_eml("non_existent_file.eml");
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_empty_file() -> Result<(), Box<dyn std::error::Error>> {
        let mut file = NamedTempFile::new()?;
        // Write empty content
        file.write_all(b"")?;
        let file_path = file.path().to_str().unwrap();

        let result = parse_eml(file_path);
        // Should handle empty files gracefully
        assert!(result.is_ok() || result.is_err()); // Either outcome is acceptable
        Ok(())
    }
}
